{"actions": [], "allow_import": 1, "allow_rename": 1, "autoname": "field:commodity_code", "creation": "2024-09-23 21:14:21.710703", "doctype": "DocType", "engine": "InnoDB", "field_order": ["commodity_code", "is_efris", "commodity_category_name", "rate", "parent_code", "is_leaf_node", "commodity_category_level", "last_synced", "column_break_cnum", "enable_status_code", "service_mark", "vat_out_scope_code", "excisable", "exclusion", "is_exempt", "is_zero_rate"], "fields": [{"fieldname": "commodity_code", "fieldtype": "Data", "label": "Commodity Code", "reqd": 1, "unique": 1}, {"fieldname": "commodity_category_name", "fieldtype": "Data", "in_list_view": 1, "label": "Commodity Category Name"}, {"fieldname": "commodity_category_level", "fieldtype": "Data", "label": "Commodity Category Level"}, {"fieldname": "parent_code", "fieldtype": "Data", "label": "Parent Code"}, {"fieldname": "enable_status_code", "fieldtype": "Data", "label": "Enable Status Code"}, {"fieldname": "excisable", "fieldtype": "Data", "label": "Excisable"}, {"fieldname": "exclusion", "fieldtype": "Data", "label": "Exclusion"}, {"fieldname": "is_exempt", "fieldtype": "Data", "label": "Is Exempt"}, {"fieldname": "is_leaf_node", "fieldtype": "Data", "label": "Is Leaf Node"}, {"fieldname": "is_zero_rate", "fieldtype": "Data", "label": "Is Zero Rate"}, {"fieldname": "rate", "fieldtype": "Data", "label": "Rate"}, {"fieldname": "service_mark", "fieldtype": "Data", "label": "Service Mark"}, {"fieldname": "vat_out_scope_code", "fieldtype": "Data", "label": "Vat Out Scope Code"}, {"fieldname": "column_break_cnum", "fieldtype": "Column Break"}, {"fieldname": "last_synced", "fieldtype": "Data", "label": "Last Synced"}, {"default": "1", "fieldname": "is_efris", "fieldtype": "Check", "label": "Is EFRIS"}], "index_web_pages_for_search": 1, "links": [], "modified": "2025-09-09 16:29:29.537796", "modified_by": "Administrator", "module": "<PERSON><PERSON>", "name": "EFRIS Commodity Code", "naming_rule": "By fieldname", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}], "row_format": "Dynamic", "sort_field": "modified", "sort_order": "DESC", "states": [], "title_field": "commodity_category_name"}