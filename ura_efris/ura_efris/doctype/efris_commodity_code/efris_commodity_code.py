# Copyright (c) 2024, bizesa.com and contributors
# For license information, please see license.txt

# import frappe
from frappe.model.document import Document
from ura_efris.api.utils import get_code_mapping
import frappe

# Define the mapping between ERPNext fields and API response keys
FIELD_MAP = {
    "commodity_category_level": "commodityCategoryLevel",
    "commodity_category_name": "commodityCategoryName",
    "enable_status_code": "enableStatusCode",
    "excisable": "excisable",
    "exclusion": "exclusion",
    "is_exempt": "isExempt",
    "is_leaf_node": "isLeafNode",
    "is_zero_rate": "isZeroRate",
    "last_synced": "nowTime",
    "parent_code": "parentCode",
    "rate": "rate",
    "service_mark": "serviceMark",
    "vat_out_scope_code": "vatOutScopeCode"
}

class EFRISCommodityCode(Document):
    def before_save(self):
        if not self.commodity_code or self.is_efris == 0:
            return
        update_commodity_code_info(self)
        

def update_commodity_code_info(doc):
    result = frappe.call(
        "ura_efris.api.t146_query_commodity_or_excise.t146_send_request",
        category_code=doc.commodity_code
    )

    if result and result[0] == "00" and result[1] == "SUCCESS":
        commodity_info = result[2].get('commodityCategory', {})
        code_mappings = get_code_mapping("T146")

        for field, api_key in FIELD_MAP.items():
            value = commodity_info.get(api_key, '')

            if api_key in code_mappings:
                mapped_value = code_mappings[api_key].get(value, value)
                value = f"{value} | {mapped_value}"

            doc.set(field, value)

    else:
        frappe.throw(
            msg=f"{result[0]}: {result[1]}<br>Please verify the Commodity Code.",
            title="Commodity Information"
        )


