{"actions": [], "allow_import": 1, "allow_rename": 1, "autoname": "field:interface_code", "creation": "2024-10-18 10:09:07.207445", "doctype": "DocType", "engine": "InnoDB", "field_order": ["interface_code", "interface_name", "description", "column_break_ytom", "request_encrypted", "response_encrypted"], "fields": [{"fieldname": "interface_code", "fieldtype": "Data", "label": "Interface Code", "reqd": 1, "unique": 1}, {"fieldname": "interface_name", "fieldtype": "Data", "in_list_view": 1, "label": "Interface Name"}, {"fieldname": "description", "fieldtype": "Small Text", "label": "Description"}, {"fieldname": "column_break_ytom", "fieldtype": "Column Break"}, {"default": "0", "fieldname": "request_encrypted", "fieldtype": "Check", "in_list_view": 1, "label": "Request Encrypted"}, {"default": "0", "fieldname": "response_encrypted", "fieldtype": "Check", "in_list_view": 1, "label": "Response Encrypted"}], "index_web_pages_for_search": 1, "links": [], "modified": "2025-09-12 05:20:48.704585", "modified_by": "Administrator", "module": "<PERSON><PERSON>", "name": "EFRIS Interface Code", "naming_rule": "By fieldname", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}], "row_format": "Dynamic", "sort_field": "modified", "sort_order": "DESC", "states": [], "title_field": "interface_name"}