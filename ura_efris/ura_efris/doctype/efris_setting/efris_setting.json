{"actions": [], "autoname": "field:company", "creation": "2025-02-24 15:59:42.100195", "doctype": "DocType", "engine": "InnoDB", "field_order": ["column_break_cgre", "company", "section_break_sfim", "tin", "mode", "url", "device_no", "private_key", "private_key_password"], "fields": [{"fieldname": "company", "fieldtype": "Link", "label": "Company", "options": "Company", "reqd": 1, "unique": 1}, {"fieldname": "column_break_cgre", "fieldtype": "Column Break"}, {"fetch_from": "company.tax_id", "fieldname": "tin", "fieldtype": "Data", "in_list_view": 1, "label": "TIN", "reqd": 1}, {"fieldname": "section_break_sfim", "fieldtype": "Section Break"}, {"fieldname": "device_no", "fieldtype": "Data", "label": "Device No", "reqd": 1}, {"depends_on": "eval:doc.mode=='Online'", "fieldname": "private_key", "fieldtype": "Attach", "label": "Private Key", "mandatory_depends_on": "eval:doc.mode=='Online'"}, {"fieldname": "url", "fieldtype": "Data", "label": "URL", "reqd": 1}, {"default": "Online", "fieldname": "mode", "fieldtype": "Select", "label": "Mode", "options": "Online\nOffline", "reqd": 1}, {"depends_on": "eval:doc.mode=='Online'", "fieldname": "private_key_password", "fieldtype": "Password", "label": "Private Key Password", "mandatory_depends_on": "eval:doc.mode=='Online'"}], "index_web_pages_for_search": 1, "links": [], "modified": "2025-09-09 16:21:39.636126", "modified_by": "Administrator", "module": "<PERSON><PERSON>", "name": "EFRIS Setting", "naming_rule": "By fieldname", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}], "row_format": "Dynamic", "sort_field": "modified", "sort_order": "DESC", "states": []}