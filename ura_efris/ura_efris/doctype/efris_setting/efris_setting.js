// Copyright (c) 2025, bizesa.com and contributors
// For license information, please see license.txt

frappe.ui.form.on("EFRIS Setting", {
    refresh: function(frm) {
        frm.add_custom_button("Server Status", function() {
            frappe.call({
                method: "ura_efris.api.t101_get_server_time.t101_send_request",
                callback: function(r) {
                    var [return_code, return_message, return_content] = r.message;

                    if (return_code === "00" && return_message === "SUCCESS") {
                        var current_time = return_content.currentTime;
                        frappe.msgprint(
                            "Server Status: Connected, <br>Server Time: " + current_time, 
                            "Server Status and Time"
                        );
                    } else {
                        frappe.msgprint("EFRIS Connection Failed: " + return_message);
                    }
                }
            });
        }, "EFRIS");

        frm.add_custom_button("Taxpayer Info", function() {
            frappe.call({
                method: "ura_efris.api.t103_log_in.t103_send_request",
                callback: function(r) {
                    var [return_code, return_message, return_content] = r.message;

                    if (return_code === "00" && return_message === "SUCCESS") {
                        frappe.msgprint(
                            "Taxpayer Info: " + JSON.stringify(return_content), 
                            "Taxpayer Info"
                        );
                    } else {
                        frappe.msgprint("Failed to retrieve Taxpayer Info: " + return_message);
                    }
                }
            });
        }, "EFRIS");
    }
});
