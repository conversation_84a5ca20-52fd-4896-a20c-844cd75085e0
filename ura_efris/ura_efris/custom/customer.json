{"custom_fields": [{"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2024-10-13 16:11:15.148536", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Customer", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "address", "fieldtype": "Small Text", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 88, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "nin_brn", "is_system_generated": 0, "is_virtual": 0, "label": "Address", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2024-10-13 16:29:37.840695", "modified_by": "Administrator", "module": null, "name": "Customer-custom_address", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2024-10-13 16:11:14.787910", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Customer", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "business_name", "fieldtype": "Data", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 86, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "column_break_bekds", "is_system_generated": 0, "is_virtual": 0, "label": "Business Name", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2024-10-13 16:30:23.522597", "modified_by": "Administrator", "module": null, "name": "Customer-custom_business_name_", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2024-10-13 16:12:31.792396", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Customer", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "column_break_bekds", "fieldtype": "Column Break", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 85, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "taxpayer_type", "is_system_generated": 0, "is_virtual": 0, "label": "", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2024-10-13 16:30:23.487841", "modified_by": "Administrator", "module": null, "name": "Customer-custom_column_break_bekds", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2024-10-13 16:11:15.254333", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Customer", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "contact_email", "fieldtype": "Data", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 83, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "contact_number", "is_system_generated": 0, "is_virtual": 0, "label": "Contact Email", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2024-10-13 16:29:47.668632", "modified_by": "Administrator", "module": null, "name": "Customer-custom_contact_email", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2024-10-13 16:11:15.369744", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Customer", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "contact_mobile", "fieldtype": "Data", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 81, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "tin", "is_system_generated": 0, "is_virtual": 0, "label": "Contact Mobile", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2024-10-13 16:29:27.332592", "modified_by": "Administrator", "module": null, "name": "Customer-custom_contact_mobile", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2024-10-13 16:11:15.490829", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Customer", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "contact_number", "fieldtype": "Data", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 82, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "contact_mobile", "is_system_generated": 0, "is_virtual": 0, "label": "Contact Number", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2024-10-13 16:29:47.633438", "modified_by": "Administrator", "module": null, "name": "Customer-custom_contact_number", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2024-10-13 15:57:56.219509", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Customer", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "efris", "fieldtype": "Tab Break", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 77, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "portal_users", "is_system_generated": 0, "is_virtual": 0, "label": "EFRIS", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2024-10-13 16:25:41.593054", "modified_by": "Administrator", "module": null, "name": "Customer-custom_efris", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2024-10-13 16:24:52.107631", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Customer", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "government_tin", "fieldtype": "Data", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 90, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "taxpayer_status", "is_system_generated": 0, "is_virtual": 0, "label": "Government TIN", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2024-10-13 16:30:10.882861", "modified_by": "Administrator", "module": null, "name": "Customer-custom_government_tin", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2024-10-13 15:59:42.355370", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Customer", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "legal_name", "fieldtype": "Data", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 79, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "taxpayer_information", "is_system_generated": 0, "is_virtual": 0, "label": "Legal Name", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2024-10-13 16:28:51.955691", "modified_by": "Administrator", "module": null, "name": "Customer-custom_legal_name", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2024-10-13 16:11:15.031022", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Customer", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "nin_brn", "fieldtype": "Data", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 87, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "business_name", "is_system_generated": 0, "is_virtual": 0, "label": "NIN BRN", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2024-10-13 16:29:37.804470", "modified_by": "Administrator", "module": null, "name": "Customer-custom_nin_brn", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2024-10-13 15:59:42.235319", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Customer", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "taxpayer_information", "fieldtype": "Section Break", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 78, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "efris", "is_system_generated": 0, "is_virtual": 0, "label": "Taxpayer Information", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2024-10-13 16:28:51.923294", "modified_by": "Administrator", "module": null, "name": "Customer-custom_taxpayer_information", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2024-10-13 16:24:51.986674", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Customer", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "taxpayer_status", "fieldtype": "Data", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 89, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "address", "is_system_generated": 0, "is_virtual": 0, "label": "Taxpayer Status", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2024-10-13 16:30:10.852376", "modified_by": "Administrator", "module": null, "name": "Customer-custom_taxpayer_status", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2024-10-13 16:24:51.822512", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Customer", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "taxpayer_type", "fieldtype": "Data", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 84, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "contact_email", "is_system_generated": 0, "is_virtual": 0, "label": "Taxpayer Type", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2024-10-13 16:29:57.915679", "modified_by": "Administrator", "module": null, "name": "Customer-custom_taxpayer_type", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2024-10-13 16:11:14.917907", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Customer", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "tin", "fieldtype": "Data", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 80, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "legal_name", "is_system_generated": 0, "is_virtual": 0, "label": "Tin", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2024-10-13 16:29:27.306136", "modified_by": "Administrator", "module": null, "name": "Customer-custom_tin", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}], "custom_perms": [], "doctype": "Customer", "links": [{"creation": "2013-06-11 14:26:44", "custom": 0, "docstatus": 0, "group": "Allowed Items", "hidden": 0, "idx": 1, "is_child_table": 0, "link_doctype": "Party Specific Item", "link_fieldname": "party", "modified": "2024-09-07 19:14:07.993725", "modified_by": "Administrator", "name": "plgvci5olt", "owner": "Administrator", "parent": "Customer", "parent_doctype": null, "parentfield": "links", "parenttype": "DocType", "table_fieldname": null}], "property_setters": [{"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "creation": "2024-10-13 16:26:30.061211", "default_value": null, "doc_type": "Customer", "docstatus": 0, "doctype_or_field": "DocType", "field_name": null, "idx": 0, "is_system_generated": 0, "modified": "2025-03-18 15:35:22.921114", "modified_by": "Administrator", "module": null, "name": "Customer-main-field_order", "owner": "Administrator", "property": "field_order", "property_type": "Data", "row_name": null, "value": "[\"basic_info\", \"naming_series\", \"salutation\", \"customer_name\", \"customer_type\", \"customer_group\", \"column_break0\", \"territory\", \"gender\", \"lead_name\", \"opportunity_name\", \"prospect_name\", \"account_manager\", \"image\", \"defaults_tab\", \"default_currency\", \"default_bank_account\", \"column_break_14\", \"default_price_list\", \"internal_customer_section\", \"is_internal_customer\", \"represents_company\", \"column_break_70\", \"companies\", \"more_info\", \"market_segment\", \"industry\", \"customer_pos_id\", \"website\", \"language\", \"column_break_45\", \"customer_details\", \"dashboard_tab\", \"contact_and_address_tab\", \"address_contacts\", \"address_html\", \"column_break1\", \"contact_html\", \"primary_address_and_contact_detail\", \"column_break_26\", \"customer_primary_address\", \"primary_address\", \"column_break_nwor\", \"customer_primary_contact\", \"mobile_no\", \"email_id\", \"tax_tab\", \"taxation_section\", \"tax_id\", \"column_break_21\", \"tax_category\", \"tax_withholding_category\", \"accounting_tab\", \"credit_limit_section\", \"payment_terms\", \"credit_limits\", \"default_receivable_accounts\", \"accounts\", \"loyalty_points_tab\", \"loyalty_program\", \"column_break_54\", \"loyalty_program_tier\", \"sales_team_tab\", \"sales_team\", \"sales_team_section\", \"default_sales_partner\", \"column_break_66\", \"default_commission_rate\", \"settings_tab\", \"so_required\", \"dn_required\", \"column_break_53\", \"is_frozen\", \"disabled\", \"portal_users_tab\", \"portal_users\", \"efris\", \"custom_taxpayer_information\", \"legal_name\", \"custom_tin\", \"custom_contact_mobile\", \"custom_contact_number\", \"custom_contact_email\", \"custom_taxpayer_type\", \"custom_column_break_bekds\", \"custom_business_name_\", \"custom_nin_brn\", \"custom_address\", \"custom_taxpayer_status\", \"custom_government_tin\"]"}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "creation": "2024-08-24 20:02:52.016933", "default_value": null, "doc_type": "Customer", "docstatus": 0, "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "naming_series", "idx": 0, "is_system_generated": 0, "modified": "2025-03-18 15:35:22.934607", "modified_by": "Administrator", "module": null, "name": "Customer-naming_series-hidden", "owner": "Administrator", "property": "hidden", "property_type": "Check", "row_name": null, "value": "1"}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "creation": "2024-08-24 20:02:51.798145", "default_value": null, "doc_type": "Customer", "docstatus": 0, "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "naming_series", "idx": 0, "is_system_generated": 0, "modified": "2025-03-18 15:35:22.947099", "modified_by": "Administrator", "module": null, "name": "Customer-naming_series-reqd", "owner": "Administrator", "property": "reqd", "property_type": "Check", "row_name": null, "value": "0"}], "sync_on_migrate": 1}