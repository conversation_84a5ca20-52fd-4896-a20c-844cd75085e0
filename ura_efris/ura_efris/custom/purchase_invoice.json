{"custom_fields": [{"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2025-03-29 23:04:09.859353", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Purchase Invoice", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "column_break_cbdfa", "fieldtype": "Column Break", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 194, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "stock_in_type", "is_system_generated": 0, "is_virtual": 0, "label": "", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2025-03-29 23:06:22.583936", "modified_by": "Administrator", "module": null, "name": "Purchase Invoice-custom_column_break_cbdfa", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2025-03-29 23:04:09.213107", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Purchase Invoice", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "efris", "fieldtype": "Tab Break", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 191, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "connections_tab", "is_system_generated": 0, "is_virtual": 0, "label": "EFRIS", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2025-03-29 23:06:41.946155", "modified_by": "Administrator", "module": null, "name": "Purchase Invoice-custom_efris", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 1, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2025-03-29 23:04:09.455907", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Purchase Invoice", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "is_efris", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 192, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "efris", "is_system_generated": 0, "is_virtual": 0, "label": "Is EFRIS", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2025-03-29 23:06:41.991419", "modified_by": "Administrator", "module": null, "name": "Purchase Invoice-custom_is_efris", "no_copy": 1, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2025-03-29 23:04:10.090090", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Purchase Invoice", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "return_code", "fieldtype": "Data", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 195, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "column_break_cbdfa", "is_system_generated": 0, "is_virtual": 0, "label": "Return Code", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2025-03-29 23:06:11.970978", "modified_by": "Administrator", "module": null, "name": "Purchase Invoice-custom_return_code", "no_copy": 1, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2025-03-29 23:04:10.314253", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Purchase Invoice", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "return_message", "fieldtype": "Data", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 196, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "return_code", "is_system_generated": 0, "is_virtual": 0, "label": "Return Message", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2025-03-29 23:05:49.972439", "modified_by": "Administrator", "module": null, "name": "Purchase Invoice-custom_return_message", "no_copy": 1, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2025-03-29 23:04:09.644088", "default": "", "depends_on": null, "description": null, "docstatus": 0, "dt": "Purchase Invoice", "fetch_from": "supplier.stock_in_type", "fetch_if_empty": 1, "fieldname": "stock_in_type", "fieldtype": "Select", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 193, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "is_efris", "is_system_generated": 0, "is_virtual": 0, "label": "Stock In Type", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2025-03-29 23:06:31.184101", "modified_by": "Administrator", "module": null, "name": "Purchase Invoice-custom_stock_in_type", "no_copy": 1, "non_negative": 0, "options": "101 | Import\n102 | Local Purchase\n103 | Manufacture/Assembling\n104 | Opening Stock", "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}], "custom_perms": [], "doctype": "Purchase Invoice", "links": [], "property_setters": [{"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "creation": "2024-08-24 20:02:53.944179", "default_value": null, "doc_type": "Purchase Invoice", "docstatus": 0, "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "base_rounded_total", "idx": 0, "is_system_generated": 0, "modified": "2024-08-24 20:02:53.944179", "modified_by": "Administrator", "module": null, "name": "Purchase Invoice-base_rounded_total-hidden", "owner": "Administrator", "property": "hidden", "property_type": "Check", "row_name": null, "value": "0"}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "creation": "2024-08-24 20:02:53.955263", "default_value": null, "doc_type": "Purchase Invoice", "docstatus": 0, "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "base_rounded_total", "idx": 0, "is_system_generated": 0, "modified": "2024-08-24 20:02:53.955263", "modified_by": "Administrator", "module": null, "name": "Purchase Invoice-base_rounded_total-print_hide", "owner": "Administrator", "property": "print_hide", "property_type": "Check", "row_name": null, "value": "1"}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "creation": "2024-08-24 20:02:53.978472", "default_value": null, "doc_type": "Purchase Invoice", "docstatus": 0, "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "disable_rounded_total", "idx": 0, "is_system_generated": 0, "modified": "2024-08-24 20:02:53.978472", "modified_by": "Administrator", "module": null, "name": "Purchase Invoice-disable_rounded_total-default", "owner": "Administrator", "property": "default", "property_type": "Text", "row_name": null, "value": "0"}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "creation": "2024-08-24 21:21:01.572736", "default_value": null, "doc_type": "Purchase Invoice", "docstatus": 0, "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "due_date", "idx": 0, "is_system_generated": 0, "modified": "2024-08-24 21:21:01.572736", "modified_by": "Administrator", "module": null, "name": "Purchase Invoice-due_date-print_hide", "owner": "Administrator", "property": "print_hide", "property_type": "Check", "row_name": null, "value": "0"}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "creation": "2024-08-24 20:02:54.163561", "default_value": null, "doc_type": "Purchase Invoice", "docstatus": 0, "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "in_words", "idx": 0, "is_system_generated": 0, "modified": "2024-08-24 20:02:54.163561", "modified_by": "Administrator", "module": null, "name": "Purchase Invoice-in_words-hidden", "owner": "Administrator", "property": "hidden", "property_type": "Check", "row_name": null, "value": "0"}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "creation": "2024-08-24 20:02:54.171668", "default_value": null, "doc_type": "Purchase Invoice", "docstatus": 0, "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "in_words", "idx": 0, "is_system_generated": 0, "modified": "2024-08-24 20:02:54.171668", "modified_by": "Administrator", "module": null, "name": "Purchase Invoice-in_words-print_hide", "owner": "Administrator", "property": "print_hide", "property_type": "Check", "row_name": null, "value": "0"}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "creation": "2025-03-29 23:09:56.125968", "default_value": null, "doc_type": "Purchase Invoice", "docstatus": 0, "doctype_or_field": "DocType", "field_name": null, "idx": 0, "is_system_generated": 0, "modified": "2025-03-29 23:09:56.125968", "modified_by": "Administrator", "module": null, "name": "Purchase Invoice-main-field_order", "owner": "Administrator", "property": "field_order", "property_type": "Data", "row_name": null, "value": "[\"title\", \"naming_series\", \"supplier\", \"supplier_name\", \"tax_id\", \"company\", \"column_break_6\", \"posting_date\", \"posting_time\", \"set_posting_time\", \"due_date\", \"column_break1\", \"is_paid\", \"is_return\", \"return_against\", \"update_outstanding_for_self\", \"update_billed_amount_in_purchase_order\", \"update_billed_amount_in_purchase_receipt\", \"apply_tds\", \"tax_withholding_category\", \"amended_from\", \"supplier_invoice_details\", \"bill_no\", \"column_break_15\", \"bill_date\", \"accounting_dimensions_section\", \"cost_center\", \"dimension_col_break\", \"project\", \"currency_and_price_list\", \"currency\", \"conversion_rate\", \"use_transaction_date_exchange_rate\", \"column_break2\", \"buying_price_list\", \"price_list_currency\", \"plc_conversion_rate\", \"ignore_pricing_rule\", \"sec_warehouse\", \"scan_barcode\", \"col_break_warehouse\", \"update_stock\", \"set_warehouse\", \"set_from_warehouse\", \"is_subcontracted\", \"rejected_warehouse\", \"supplier_warehouse\", \"items_section\", \"items\", \"section_break_26\", \"total_qty\", \"total_net_weight\", \"column_break_50\", \"base_total\", \"base_net_total\", \"column_break_28\", \"total\", \"net_total\", \"tax_withholding_net_total\", \"base_tax_withholding_net_total\", \"taxes_section\", \"tax_category\", \"taxes_and_charges\", \"column_break_58\", \"shipping_rule\", \"column_break_49\", \"incoterm\", \"named_place\", \"section_break_51\", \"taxes\", \"totals\", \"base_taxes_and_charges_added\", \"base_taxes_and_charges_deducted\", \"base_total_taxes_and_charges\", \"column_break_40\", \"taxes_and_charges_added\", \"taxes_and_charges_deducted\", \"total_taxes_and_charges\", \"section_break_49\", \"base_grand_total\", \"base_rounding_adjustment\", \"base_rounded_total\", \"base_in_words\", \"column_break8\", \"grand_total\", \"rounding_adjustment\", \"use_company_roundoff_cost_center\", \"rounded_total\", \"in_words\", \"total_advance\", \"outstanding_amount\", \"disable_rounded_total\", \"section_break_44\", \"apply_discount_on\", \"base_discount_amount\", \"column_break_46\", \"additional_discount_percentage\", \"discount_amount\", \"tax_withheld_vouchers_section\", \"tax_withheld_vouchers\", \"sec_tax_breakup\", \"other_charges_calculation\", \"pricing_rule_details\", \"pricing_rules\", \"raw_materials_supplied\", \"supplied_items\", \"payments_tab\", \"payments_section\", \"mode_of_payment\", \"base_paid_amount\", \"clearance_date\", \"col_br_payments\", \"cash_bank_account\", \"paid_amount\", \"advances_section\", \"allocate_advances_automatically\", \"only_include_allocated_payments\", \"get_advances\", \"advances\", \"advance_tax\", \"write_off\", \"write_off_amount\", \"base_write_off_amount\", \"column_break_61\", \"write_off_account\", \"write_off_cost_center\", \"address_and_contact_tab\", \"section_addresses\", \"supplier_address\", \"address_display\", \"col_break_address\", \"contact_person\", \"contact_display\", \"contact_mobile\", \"contact_email\", \"company_shipping_address_section\", \"shipping_address\", \"column_break_126\", \"shipping_address_display\", \"company_billing_address_section\", \"billing_address\", \"column_break_130\", \"billing_address_display\", \"terms_tab\", \"payment_schedule_section\", \"payment_terms_template\", \"ignore_default_payment_terms_template\", \"payment_schedule\", \"terms_section_break\", \"tc_name\", \"terms\", \"more_info_tab\", \"status_section\", \"status\", \"column_break_177\", \"per_received\", \"accounting_details_section\", \"credit_to\", \"party_account_currency\", \"is_opening\", \"against_expense_account\", \"column_break_63\", \"unrealized_profit_loss_account\", \"subscription_section\", \"subscription\", \"auto_repeat\", \"update_auto_repeat_reference\", \"column_break_114\", \"from_date\", \"to_date\", \"printing_settings\", \"letter_head\", \"group_same_items\", \"column_break_112\", \"select_print_heading\", \"language\", \"sb_14\", \"on_hold\", \"release_date\", \"cb_17\", \"hold_comment\", \"additional_info_section\", \"is_internal_supplier\", \"represents_company\", \"supplier_group\", \"column_break_147\", \"inter_company_invoice_reference\", \"is_old_subcontracting_flow\", \"remarks\", \"connections_tab\", \"efris\", \"is_efris\", \"stock_in_type\", \"column_break_cbdfa\", \"return_code\", \"return_message\"]"}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "creation": "2024-08-24 21:21:01.576703", "default_value": null, "doc_type": "Purchase Invoice", "docstatus": 0, "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "payment_schedule", "idx": 0, "is_system_generated": 0, "modified": "2024-08-24 21:21:01.576703", "modified_by": "Administrator", "module": null, "name": "Purchase Invoice-payment_schedule-print_hide", "owner": "Administrator", "property": "print_hide", "property_type": "Check", "row_name": null, "value": "1"}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "creation": "2024-08-24 20:02:53.963244", "default_value": null, "doc_type": "Purchase Invoice", "docstatus": 0, "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "rounded_total", "idx": 0, "is_system_generated": 0, "modified": "2024-08-24 20:02:53.963244", "modified_by": "Administrator", "module": null, "name": "Purchase Invoice-rounded_total-hidden", "owner": "Administrator", "property": "hidden", "property_type": "Check", "row_name": null, "value": "0"}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "creation": "2024-08-24 20:02:53.970901", "default_value": null, "doc_type": "Purchase Invoice", "docstatus": 0, "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "rounded_total", "idx": 0, "is_system_generated": 0, "modified": "2024-08-24 20:02:53.970901", "modified_by": "Administrator", "module": null, "name": "Purchase Invoice-rounded_total-print_hide", "owner": "Administrator", "property": "print_hide", "property_type": "Check", "row_name": null, "value": "0"}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "creation": "2024-08-24 20:02:55.868981", "default_value": null, "doc_type": "Purchase Invoice", "docstatus": 0, "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "scan_barcode", "idx": 0, "is_system_generated": 1, "modified": "2024-08-24 20:02:55.868981", "modified_by": "Administrator", "module": null, "name": "Purchase Invoice-scan_barcode-hidden", "owner": "Administrator", "property": "hidden", "property_type": "Check", "row_name": null, "value": "0"}], "sync_on_migrate": 1}