{"custom_fields": [{"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2025-03-21 16:15:43.208136", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Supplier", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "efris", "fieldtype": "Tab Break", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 65, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "column_break_1mqv", "is_system_generated": 0, "is_virtual": 0, "label": "EFRIS", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2025-03-21 16:16:10.439067", "modified_by": "Administrator", "module": null, "name": "Supplier-custom_efris", "no_copy": 0, "non_negative": 0, "options": "", "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2025-03-21 16:15:43.342661", "default": "102 | Local Purchase", "depends_on": null, "description": null, "docstatus": 0, "dt": "Supplier", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "stock_in_type", "fieldtype": "Select", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 66, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "efris", "is_system_generated": 0, "is_virtual": 0, "label": "Stock In Type", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2025-03-21 16:16:22.232774", "modified_by": "Administrator", "module": null, "name": "Supplier-custom_stock_in_type", "no_copy": 0, "non_negative": 0, "options": "101 | Import\n102 | Local Purchase\n103 | Manufacture/Assembling\n104 | Opening Stock", "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}], "custom_perms": [], "doctype": "Supplier", "links": [{"creation": "2013-01-10 16:34:11", "custom": 0, "docstatus": 0, "group": "Allowed Items", "hidden": 0, "idx": 1, "is_child_table": 0, "link_doctype": "Party Specific Item", "link_fieldname": "party", "modified": "2024-08-24 21:20:35.246764", "modified_by": "Administrator", "name": "837g87inhe", "owner": "Administrator", "parent": "Supplier", "parent_doctype": null, "parentfield": "links", "parenttype": "DocType", "table_fieldname": null}], "property_setters": [{"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "creation": "2025-03-21 16:19:07.991628", "default_value": null, "doc_type": "Supplier", "docstatus": 0, "doctype_or_field": "DocType", "field_name": null, "idx": 0, "is_system_generated": 0, "modified": "2025-03-21 16:19:07.991628", "modified_by": "Administrator", "module": null, "name": "Supplier-main-field_order", "owner": "Administrator", "property": "field_order", "property_type": "Data", "row_name": null, "value": "[\"naming_series\", \"supplier_name\", \"country\", \"column_break0\", \"supplier_group\", \"supplier_type\", \"is_transporter\", \"image\", \"defaults_section\", \"default_currency\", \"default_bank_account\", \"column_break_10\", \"default_price_list\", \"internal_supplier_section\", \"is_internal_supplier\", \"represents_company\", \"column_break_16\", \"companies\", \"column_break2\", \"supplier_details\", \"column_break_30\", \"website\", \"language\", \"dashboard_tab\", \"tax_tab\", \"tax_id\", \"column_break_27\", \"tax_category\", \"tax_withholding_category\", \"contact_and_address_tab\", \"address_contacts\", \"address_html\", \"column_break1\", \"contact_html\", \"primary_address_and_contact_detail_section\", \"column_break_44\", \"supplier_primary_address\", \"primary_address\", \"column_break_mglr\", \"supplier_primary_contact\", \"mobile_no\", \"email_id\", \"accounting_tab\", \"payment_terms\", \"default_accounts_section\", \"accounts\", \"settings_tab\", \"allow_purchase_invoice_creation_without_purchase_order\", \"allow_purchase_invoice_creation_without_purchase_receipt\", \"column_break_54\", \"is_frozen\", \"disabled\", \"warn_rfqs\", \"warn_pos\", \"prevent_rfqs\", \"prevent_pos\", \"block_supplier_section\", \"on_hold\", \"hold_type\", \"column_break_59\", \"release_date\", \"portal_users_tab\", \"portal_users\", \"column_break_1mqv\", \"efris\", \"stock_in_type\"]"}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "creation": "2024-08-24 20:02:50.944180", "default_value": null, "doc_type": "Supplier", "docstatus": 0, "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "naming_series", "idx": 0, "is_system_generated": 0, "modified": "2024-08-24 20:02:50.944180", "modified_by": "Administrator", "module": null, "name": "Supplier-naming_series-hidden", "owner": "Administrator", "property": "hidden", "property_type": "Check", "row_name": null, "value": "1"}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "creation": "2024-08-24 20:02:50.757927", "default_value": null, "doc_type": "Supplier", "docstatus": 0, "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "naming_series", "idx": 0, "is_system_generated": 0, "modified": "2024-08-24 20:02:50.757927", "modified_by": "Administrator", "module": null, "name": "Supplier-naming_series-reqd", "owner": "Administrator", "property": "reqd", "property_type": "Check", "row_name": null, "value": "0"}], "sync_on_migrate": 1}