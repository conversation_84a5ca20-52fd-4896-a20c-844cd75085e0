{"custom_fields": [{"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2025-03-23 22:06:40.730244", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Purchase Receipt", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "column_break_2iohr", "fieldtype": "Column Break", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 147, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "stock_in_type", "is_system_generated": 0, "is_virtual": 0, "label": "", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2025-03-23 22:08:10.851285", "modified_by": "Administrator", "module": null, "name": "Purchase Receipt-custom_column_break_2iohr", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2025-03-22 11:19:09.197495", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Purchase Receipt", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "efris", "fieldtype": "Tab Break", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 145, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "connections_tab", "is_system_generated": 0, "is_virtual": 0, "label": "EFRIS", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2025-03-22 11:22:25.370911", "modified_by": "Administrator", "module": null, "name": "Purchase Receipt-custom_efris", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2025-03-23 22:06:40.517443", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Purchase Receipt", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "is_efris", "fieldtype": "Check", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 145, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "efris", "is_system_generated": 0, "is_virtual": 0, "label": "Is EFRIS", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2025-03-23 22:07:26.807894", "modified_by": "Administrator", "module": null, "name": "Purchase Receipt-custom_is_efris", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 0, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2025-03-23 22:06:40.870191", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Purchase Receipt", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "return_code", "fieldtype": "Data", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 148, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "column_break_2iohr", "is_system_generated": 0, "is_virtual": 0, "label": "Return Code", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2025-03-23 22:08:10.879788", "modified_by": "Administrator", "module": null, "name": "Purchase Receipt-custom_return_code", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2025-03-23 22:06:41.017858", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Purchase Receipt", "fetch_from": null, "fetch_if_empty": 0, "fieldname": "return_message", "fieldtype": "Data", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 149, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "return_code", "is_system_generated": 0, "is_virtual": 0, "label": "Return Message", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2025-03-23 22:07:53.453982", "modified_by": "Administrator", "module": null, "name": "Purchase Receipt-custom_return_message", "no_copy": 0, "non_negative": 0, "options": null, "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "allow_in_quick_entry": 0, "allow_on_submit": 0, "bold": 0, "collapsible": 0, "collapsible_depends_on": null, "columns": 0, "creation": "2025-03-22 11:19:09.389772", "default": null, "depends_on": null, "description": null, "docstatus": 0, "dt": "Purchase Receipt", "fetch_from": "supplier.stock_in_type", "fetch_if_empty": 1, "fieldname": "stock_in_type", "fieldtype": "Select", "hidden": 0, "hide_border": 0, "hide_days": 0, "hide_seconds": 0, "idx": 146, "ignore_user_permissions": 0, "ignore_xss_filter": 0, "in_global_search": 0, "in_list_view": 0, "in_preview": 0, "in_standard_filter": 0, "insert_after": "is_efris", "is_system_generated": 0, "is_virtual": 0, "label": "Stock In Type", "length": 0, "link_filters": null, "mandatory_depends_on": null, "modified": "2025-03-23 22:07:26.815357", "modified_by": "Administrator", "module": null, "name": "Purchase Receipt-custom_stock_in_type", "no_copy": 0, "non_negative": 0, "options": "101 | Import\n102 | Local Purchase\n103 | Manufacture/Assembling\n104 | Opening Stock", "owner": "Administrator", "permlevel": 0, "placeholder": null, "precision": "", "print_hide": 0, "print_hide_if_no_value": 0, "print_width": null, "read_only": 0, "read_only_depends_on": null, "report_hide": 0, "reqd": 0, "search_index": 0, "show_dashboard": 0, "sort_options": 0, "translatable": 1, "unique": 0, "width": null}], "custom_perms": [], "doctype": "Purchase Receipt", "links": [], "property_setters": [{"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "creation": "2024-08-24 20:02:53.989810", "default_value": null, "doc_type": "Purchase Receipt", "docstatus": 0, "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "base_rounded_total", "idx": 0, "is_system_generated": 0, "modified": "2024-08-24 20:02:53.989810", "modified_by": "Administrator", "module": null, "name": "Purchase Receipt-base_rounded_total-hidden", "owner": "Administrator", "property": "hidden", "property_type": "Check", "row_name": null, "value": "0"}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "creation": "2024-08-24 20:02:54.001427", "default_value": null, "doc_type": "Purchase Receipt", "docstatus": 0, "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "base_rounded_total", "idx": 0, "is_system_generated": 0, "modified": "2024-08-24 20:02:54.001427", "modified_by": "Administrator", "module": null, "name": "Purchase Receipt-base_rounded_total-print_hide", "owner": "Administrator", "property": "print_hide", "property_type": "Check", "row_name": null, "value": "1"}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "creation": "2024-08-24 20:02:54.034116", "default_value": null, "doc_type": "Purchase Receipt", "docstatus": 0, "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "disable_rounded_total", "idx": 0, "is_system_generated": 0, "modified": "2024-08-24 20:02:54.034116", "modified_by": "Administrator", "module": null, "name": "Purchase Receipt-disable_rounded_total-default", "owner": "Administrator", "property": "default", "property_type": "Text", "row_name": null, "value": "0"}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "creation": "2024-08-24 20:02:54.180824", "default_value": null, "doc_type": "Purchase Receipt", "docstatus": 0, "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "in_words", "idx": 0, "is_system_generated": 0, "modified": "2024-08-24 20:02:54.180824", "modified_by": "Administrator", "module": null, "name": "Purchase Receipt-in_words-hidden", "owner": "Administrator", "property": "hidden", "property_type": "Check", "row_name": null, "value": "0"}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "creation": "2024-08-24 20:02:54.190954", "default_value": null, "doc_type": "Purchase Receipt", "docstatus": 0, "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "in_words", "idx": 0, "is_system_generated": 0, "modified": "2024-08-24 20:02:54.190954", "modified_by": "Administrator", "module": null, "name": "Purchase Receipt-in_words-print_hide", "owner": "Administrator", "property": "print_hide", "property_type": "Check", "row_name": null, "value": "0"}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "creation": "2025-03-23 22:06:40.400524", "default_value": null, "doc_type": "Purchase Receipt", "docstatus": 0, "doctype_or_field": "DocType", "field_name": null, "idx": 0, "is_system_generated": 0, "modified": "2025-03-23 22:06:40.400524", "modified_by": "Administrator", "module": null, "name": "Purchase Receipt-main-field_order", "owner": "Administrator", "property": "field_order", "property_type": "Data", "row_name": null, "value": "[\"supplier_section\", \"column_break0\", \"title\", \"naming_series\", \"supplier\", \"supplier_name\", \"supplier_delivery_note\", \"subcontracting_receipt\", \"column_break1\", \"posting_date\", \"posting_time\", \"set_posting_time\", \"column_break_12\", \"company\", \"apply_putaway_rule\", \"is_return\", \"return_against\", \"accounting_dimensions_section\", \"cost_center\", \"dimension_col_break\", \"project\", \"currency_and_price_list\", \"currency\", \"conversion_rate\", \"column_break2\", \"buying_price_list\", \"price_list_currency\", \"plc_conversion_rate\", \"ignore_pricing_rule\", \"sec_warehouse\", \"scan_barcode\", \"column_break_31\", \"set_warehouse\", \"set_from_warehouse\", \"col_break_warehouse\", \"rejected_warehouse\", \"is_subcontracted\", \"supplier_warehouse\", \"items_section\", \"items\", \"section_break0\", \"total_qty\", \"total_net_weight\", \"column_break_43\", \"base_total\", \"base_net_total\", \"column_break_27\", \"total\", \"net_total\", \"tax_withholding_net_total\", \"base_tax_withholding_net_total\", \"taxes_charges_section\", \"tax_category\", \"taxes_and_charges\", \"shipping_col\", \"shipping_rule\", \"column_break_53\", \"incoterm\", \"named_place\", \"taxes_section\", \"taxes\", \"totals\", \"base_taxes_and_charges_added\", \"base_taxes_and_charges_deducted\", \"base_total_taxes_and_charges\", \"column_break3\", \"taxes_and_charges_added\", \"taxes_and_charges_deducted\", \"total_taxes_and_charges\", \"section_break_46\", \"base_grand_total\", \"base_rounding_adjustment\", \"base_rounded_total\", \"base_in_words\", \"column_break_50\", \"grand_total\", \"rounding_adjustment\", \"rounded_total\", \"in_words\", \"disable_rounded_total\", \"section_break_42\", \"apply_discount_on\", \"base_discount_amount\", \"column_break_44\", \"additional_discount_percentage\", \"discount_amount\", \"sec_tax_breakup\", \"other_charges_calculation\", \"pricing_rule_details\", \"pricing_rules\", \"raw_material_details\", \"get_current_stock\", \"supplied_items\", \"address_and_contact_tab\", \"section_addresses\", \"supplier_address\", \"address_display\", \"col_break_address\", \"contact_person\", \"contact_display\", \"contact_mobile\", \"contact_email\", \"section_break_98\", \"shipping_address\", \"column_break_100\", \"shipping_address_display\", \"billing_address_section\", \"billing_address\", \"column_break_104\", \"billing_address_display\", \"terms_tab\", \"tc_name\", \"terms\", \"more_info_tab\", \"status_section\", \"status\", \"column_break4\", \"per_billed\", \"per_returned\", \"subscription_detail\", \"auto_repeat\", \"printing_settings\", \"letter_head\", \"group_same_items\", \"column_break_97\", \"select_print_heading\", \"language\", \"transporter_info\", \"transporter_name\", \"column_break5\", \"lr_no\", \"lr_date\", \"additional_info_section\", \"instructions\", \"is_internal_supplier\", \"represents_company\", \"inter_company_reference\", \"column_break_131\", \"remarks\", \"range\", \"amended_from\", \"is_old_subcontracting_flow\", \"other_details\", \"connections_tab\", \"efris\", \"is_efris\", \"stock_in_type\", \"column_break_yykj\", \"return_code\", \"return_message\"]"}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "creation": "2024-10-31 09:27:34.719199", "default_value": null, "doc_type": "Purchase Receipt", "docstatus": 0, "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "provisional_expense_account", "idx": 0, "is_system_generated": 0, "modified": "2024-10-31 09:27:34.719199", "modified_by": "Administrator", "module": null, "name": "Purchase Receipt-provisional_expense_account-hidden", "owner": "Administrator", "property": "hidden", "property_type": "Check", "row_name": null, "value": "1"}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "creation": "2024-08-24 20:02:54.011371", "default_value": null, "doc_type": "Purchase Receipt", "docstatus": 0, "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "rounded_total", "idx": 0, "is_system_generated": 0, "modified": "2024-08-24 20:02:54.011371", "modified_by": "Administrator", "module": null, "name": "Purchase Receipt-rounded_total-hidden", "owner": "Administrator", "property": "hidden", "property_type": "Check", "row_name": null, "value": "0"}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "creation": "2024-08-24 20:02:54.024301", "default_value": null, "doc_type": "Purchase Receipt", "docstatus": 0, "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "rounded_total", "idx": 0, "is_system_generated": 0, "modified": "2024-08-24 20:02:54.024301", "modified_by": "Administrator", "module": null, "name": "Purchase Receipt-rounded_total-print_hide", "owner": "Administrator", "property": "print_hide", "property_type": "Check", "row_name": null, "value": "0"}, {"_assign": null, "_comments": null, "_liked_by": null, "_user_tags": null, "creation": "2024-08-24 20:02:55.944235", "default_value": null, "doc_type": "Purchase Receipt", "docstatus": 0, "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "scan_barcode", "idx": 0, "is_system_generated": 1, "modified": "2024-08-24 20:02:55.944235", "modified_by": "Administrator", "module": null, "name": "Purchase Receipt-scan_barcode-hidden", "owner": "Administrator", "property": "hidden", "property_type": "Check", "row_name": null, "value": "0"}], "sync_on_migrate": 1}