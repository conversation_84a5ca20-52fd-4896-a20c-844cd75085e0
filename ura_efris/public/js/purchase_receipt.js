frappe.ui.form.on("Purchase Receipt", {
    refresh: function (frm) {
        if (frm.doc.docstatus === 1) {
            frm.add_custom_button("Send to EFRIS", function () {
                if (frm.is_dirty()) {
                    frappe.msgprint({
                        title: __('Unsaved Changes'),
                        message: __('Please save the document before sending to EFRIS.'),
                        indicator: 'red'
                    });
                    return;
                }
                frappe.call({
                    method: "ura_efris.api.t131_goods_stock_maintain.t131_send_request",
                    args: {
                        doctype: frm.doc.doctype,
                        name: frm.doc.name
                    },
                    callback: function () {
                        frm.reload_doc();
                    }
                });
            }, "EFRIS");
        }
    }
});