import base64
import json
import zlib
import frappe
import requests
from frappe.utils import now_datetime
from frappe.utils.password import get_decrypted_password
from cryptography.hazmat.primitives.serialization import pkcs12
from cryptography.hazmat.backends import default_backend
from cryptography.hazmat.primitives.asymmetric import padding
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.primitives.padding import PKCS7
from ura_efris.api.helper import log_efris_request

# Constants
CACHE_KEY_AES = "efris_aes_key"
SUCCESS_CODE = "00"
SUCCESS_MESSAGE = "SUCCESS"

def get_efris_settings(doc=None):
    """Get the company settings for EFRIS from document or default company."""
    company = doc.company if doc and doc.company else frappe.db.get_single_value("Global Defaults", "default_company")
    return frappe.get_cached_doc("EFRIS Setting", company)

def get_private_key(efris_settings):
    """Load the private key from the file specified in EFRIS settings."""
    file_path = frappe.get_site_path(efris_settings.private_key.lstrip('/'))
    password = get_decrypted_password("EFRIS Setting", efris_settings.name, "private_key_password").encode()
    with open(file_path, "rb") as file:
        key_data = pkcs12.load_key_and_certificates(file.read(), password, default_backend())[0]
    return key_data

def get_password_key(efris_settings):
    """Fetch the password key from EFRIS API using T104 interface."""
    payload, _ = prepare_payload("T104", efris_settings.tin, efris_settings.device_no, "", efris_settings)
    response = requests.post(efris_settings.url, json=payload).json()
    return_code, return_message, content = process_response(response)
    if return_code == SUCCESS_CODE and return_message == SUCCESS_MESSAGE:
        return base64.b64decode(content["passowrdDes"])
    return None

def get_aes_key(efris_settings):
    """Get AES encryption key from cache or fetch new one if not available."""
    cached_aes = frappe.cache().get_value(CACHE_KEY_AES)
    if cached_aes:
        return base64.b64decode(cached_aes)

    password_des = get_password_key(efris_settings)
    private_key = get_private_key(efris_settings)

    aes_key = private_key.decrypt(
        password_des,
        padding.PKCS1v15()
    )

    frappe.cache().set_value(CACHE_KEY_AES, aes_key, expires_in_sec=86400)
    return base64.b64decode(aes_key)

def encrypt_with_aes(data, key):
    """Encrypt data using AES and return base64 encoded string."""
    padder = PKCS7(128).padder()
    padded_data = padder.update(data.encode()) + padder.finalize()
    cipher = Cipher(algorithms.AES(key), modes.ECB(), backend=default_backend()).encryptor()
    encrypted_data = cipher.update(padded_data) + cipher.finalize()
    return base64.b64encode(encrypted_data).decode()

def decrypt_with_aes(encrypted_data, key):
    """Decrypt base64 encoded data using AES."""
    cipher = Cipher(algorithms.AES(key), modes.ECB(), backend=default_backend()).decryptor()
    decrypted_padded = cipher.update(base64.b64decode(encrypted_data)) + cipher.finalize()
    unpadder = PKCS7(128).unpadder()
    decrypted_data = unpadder.update(decrypted_padded) + unpadder.finalize()
    return decrypted_data.decode()

def sign_data(private_key, encrypted_content):
    """Sign encrypted data with private key and return base64 encoded signature."""
    signature = private_key.sign(encrypted_content, padding.PKCS1v15(), hashes.SHA1())
    return base64.b64encode(signature).decode()

def encode_to_base64(content):
    """Convert string or JSON-serializable object to base64 encoded string."""
    if isinstance(content, (list, dict)):
        content = json.dumps(content)
    return base64.b64encode(content.encode()).decode()

def decode_from_base64(encoded_content, zip_code):
    """Decode base64 data, unzipping if zip_code is '1'."""
    if zip_code == "1":
        return zlib.decompress(base64.b64decode(encoded_content)).decode()
    return base64.b64decode(encoded_content).decode()

def process_response(response):
    """Extract return code, message and content from EFRIS API response."""
    data_content = response["data"]["content"]
    zip_code = response["data"]["dataDescription"]["zipCode"]
    content = json.loads(decode_from_base64(data_content, zip_code)) if data_content else ""
    return_code = response["returnStateInfo"].get("returnCode", "")
    return_message = response["returnStateInfo"].get("returnMessage", "")
    return return_code, return_message, content

def create_basic_info(interface_code, tin, device_no):
    """Build the basic metadata dictionary needed for every EFRIS request."""
    return {
        "appId": "AP04",
        "version": "1.1.20191201",
        "dataExchangeId": "9230489223014123",
        "interfaceCode": interface_code,
        "requestCode": "TP",
        "requestTime": now_datetime().strftime("%Y-%m-%d %H:%M:%S"),
        "responseCode": "TA",
        "userName": "1013752737",
        "deviceMAC": "FFFFFFFFFFFF",
        "deviceNo": device_no,
        "tin": tin,
        "taxpayerID": "1",
        "longitude": "116.397128",
        "latitude": "39.916527",
        "extendField": {
            "responseDateFormat": "dd/MM/yyyy",
            "responseTimeFormat": "dd/MM/yyyy HH:mm:ss",
            "operatorName": "administrator"
        }
    }

def prepare_payload(interface_code, tin, device_no, content, efris_settings):
    """Prepare the payload for EFRIS API with encryption if needed based on mode."""
    # Offline mode or special cases don’t need encryption
    if efris_settings.mode == "Offline" or interface_code in ["T104", "T101"]:
        return {
            "data": {
                "content": encode_to_base64(content),
                "signature": "",
                "dataDescription": {"codeType": "0", "encryptCode": "1", "zipCode": "0"}
            },
            "globalInfo": create_basic_info(interface_code, tin, device_no),
            "returnStateInfo": {"returnCode": "", "returnMessage": ""}
        }, None

    # Online mode needs encryption and signing
    private_key = get_private_key(efris_settings)
    aes_key = get_aes_key(efris_settings)
    if not aes_key:
        return None, None

    encrypted_content = encrypt_with_aes(json.dumps(content), aes_key)
    signature = sign_data(private_key, encrypted_content.encode())

    return {
        "data": {
            "content": encrypted_content,
            "signature": signature,
            "dataDescription": {"codeType": "1", "encryptCode": "2", "zipCode": "0"}
        },
        "globalInfo": create_basic_info(interface_code, tin, device_no),
        "returnStateInfo": {"returnCode": "", "returnMessage": ""}
    }, aes_key

def send_request(interface_code, content="", doc=None):
    """Send a request to EFRIS API and process the response based on mode."""
    efris_settings = get_efris_settings(doc)
    payload, aes_key = prepare_payload(interface_code, efris_settings.tin, efris_settings.device_no, content, efris_settings)
    if not payload:
        return None, "ERROR", "Could not prepare data to send"

    response = None
    return_code = None
    return_message = None
    response_content = None
    error_message = None

    try:
        response = requests.post(efris_settings.url, json=payload).json()

        # Offline or special case: no decryption needed
        if efris_settings.mode == "Offline" or interface_code == "T101":
            return_code, return_message, response_content = process_response(response)
        else:
            # Online mode: decrypt the response
            decrypted_content = decrypt_with_aes(response["data"]["content"], aes_key)
            return_code = response["returnStateInfo"]["returnCode"]
            return_message = response["returnStateInfo"]["returnMessage"]
            response_content = json.loads(decrypted_content)

    except requests.exceptions.RequestException as e:
        error_message = f"Network error: {str(e)}"
        return_code = None
        return_message = "ERROR"
        response_content = error_message
    except KeyError as e:
        error_message = f"Invalid response format: missing key {str(e)}"
        return_code = None
        return_message = "ERROR"
        response_content = error_message
    except json.JSONDecodeError as e:
        error_message = f"JSON decode error: {str(e)}"
        return_code = None
        return_message = "ERROR"
        response_content = error_message
    except Exception as e:
        error_message = f"Unexpected error: {str(e)}"
        return_code = None
        return_message = "ERROR"
        response_content = error_message

    # Always log the request, whether successful or failed
    try:
        log_efris_request(
            interface_code=interface_code,
            request_payload=payload,
            response_data=response,
            return_code=return_code,
            return_message=return_message,
            request_content=content,
            response_content=response_content,
            reference_doctype=doc.doctype if doc else None,
            reference_document=doc.name if doc else None
        )
    except Exception as log_error:
        # If logging fails, at least log the error to Frappe's error log
        frappe.log_error(
            message=f"Failed to log EFRIS request: {str(log_error)}",
            title=f"EFRIS Logging Error for {interface_code}"
        )

    return return_code, return_message, response_content

def get_code_mapping(interface_code):
    """Fetch and organize code mappings from the database for given interface code."""
    data = frappe.db.get_all(
        "EFRIS Code Mapping",
        filters={"interface_code": interface_code},
        fields=["key", "code", "value"]
    )
    mappings = {}
    for item in data:
        mappings.setdefault(item['key'], {})[item['code']] = item['value']
    return mappings