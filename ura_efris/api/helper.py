import json
import frappe


def _to_json(value):
    """Safely convert dict/list/other to JSON or string."""
    if value is None:
        return None
    if isinstance(value, (dict, list)):
        return json.dumps(value, indent=2, default=str)
    return str(value)


def log_efris_request(
    interface_code,
    request_payload=None,
    response_data=None,
    return_code=None,
    return_message=None,
    request_content=None,
    response_content=None,
    reference_doctype=None,
    reference_document=None,
    commit=False,
):
    """
    Create an EFRIS Log entry to track API requests and responses.

    Args:
        interface_code (str): EFRIS interface code (e.g., T101, T103)
        request_payload (dict/str, optional): Full request payload
        response_data (dict/str, optional): Full response received
        return_code (str, optional): EFRIS return code
        return_message (str, optional): EFRIS return message
        request_content (dict/str, optional): Content sent
        response_content (dict/str, optional): Content received
        reference_doctype (str, optional): Related doctype
        reference_document (str, optional): Related document name
        commit (bool, optional): Force db commit immediately
    """
    try:
        status = "Success" if return_code == "00" else "Error"

        log_doc = frappe.new_doc("EFRIS Log")
        log_doc.update({
            "interface_code": interface_code,
            "reference_doctype": reference_doctype,
            "reference_document": reference_document,
            "user": frappe.session.user if frappe.session.user else "Guest",
            "status": status,
            "return_code": return_code,
            "return_message": return_message,
            "request_content": _to_json(request_content),
            "response_content": _to_json(response_content),
            "request": _to_json(request_payload),
            "response": _to_json(response_data),
        })

        log_doc.insert(ignore_permissions=True)

        if commit:
            frappe.db.commit()

        return log_doc.name

    except Exception as e:
        frappe.log_error(
            message=frappe.get_traceback(),
            title=f"EFRIS Logging Error: {str(e)}"
        )
        return None