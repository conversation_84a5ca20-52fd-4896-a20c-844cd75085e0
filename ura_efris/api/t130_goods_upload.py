import frappe
from ura_efris.api.utils import send_request


@frappe.whitelist()
def t130_send_request(doc, operation_type):
    content = [
        { 
            "operationType": operation_type,
            "goodsName": doc.item_name,
            "goodsCode": doc.item_code,
            "measureUnit": doc.measure_unit,
            "unitPrice": doc.unit_price,
            "currency": frappe.get_value("Currency", doc.currency, "currency_code"),
            "commodityCategoryId": doc.commodity_category_id,
            "haveExciseTax": "102",                            #101:Yes 102:No
            "description": doc.item_name,
            "stockPrewarning": "10",
            "havePieceUnit": "102"                             #101:Yes 102:No
        }
    ]

    return send_request("T130", content)
