import frappe
from ura_efris.api.utils import send_request

interface_code = "T131"

@frappe.whitelist()
def t131_send_request(doctype, name):
    doc = frappe.get_doc(doctype, name)
    if doc.return_code == "00":
        frappe.throw(f"{doc.doctype} has already been sent to EFRIS")
    if doc.is_efris==0:
        frappe.throw(f"{doc.doctype} is not EFRIS enabled")
    if doc.docstatus != 1:
        frappe.throw("Submit the document before sending to EFRIS")
    if doc.doctype== "Purchase Invoice" and doc.update_stock==0:
        frappe.throw("Update stock is not checked")
    elif doc.doctype in ["Purchase Receipt", "Purchase Invoice"]:
        goods_stock_in_items = [
            {
                "goodsCode": item.item_code,
                "measureUnit": frappe.get_value("Item", item.item_code, "measure_unit"),
                "quantity": float(item.stock_qty) if doc.is_return == 0 else -float(item.stock_qty),
                "unitPrice": float(item.stock_uom_rate * doc.conversion_rate)
            }
            for item in doc.items
        ]

        content = {
            "goodsStockIn": {
                "operationType": "101" if doc.is_return == 0 else "102",
                "supplierName": doc.supplier_name if doc.is_return == 0 else "",
                "stockInType": doc.stock_in_type.split("|")[0].strip() if doc.is_return == 0 else "",
                "adjustType": "" if doc.is_return == 0 else "104",
                "remarks": "" if doc.is_return == 0 else "Purchase Return",
                "rollBackIfError": "1"
            },
            "goodsStockInItem": goods_stock_in_items
        }

        return_code, return_message, return_content = send_request(interface_code, content, doc)
        
        doc.db_set({
            "return_code": return_code,
            "return_message": return_message
        })

        if return_code == "00" and return_message == "SUCCESS":
            frappe.msgprint(f"{doc.doctype} sent to EFRIS successfully")
        else:
            frappe.msgprint(f"Error: <b>{return_code}</b> - {return_message}")

        return return_code, return_message, return_content
    else:
        frappe.throw("Something went wrong, please contact support")
        return None, None, None