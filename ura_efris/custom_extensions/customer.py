import frappe
from ura_efris.api.utils import get_code_mapping

# Define a mapping between ERPNext fields and API response keys
FIELD_MAP = {
    'address': 'address',
    'business_name': 'businessName',
    'contact_email': 'contactEmail',
    'contact_mobile': 'contactMobile',
    'contact_number': 'contactNumber',
    'government_tin': 'governmentTIN',
    'legal_name': 'legalName',
    'nin_brn': 'ninBrn',
    'taxpayer_status': 'taxpayerStatus',
    'taxpayer_type': 'taxpayerType',
    'tin': 'tin'
}

def clear_taxpayer_fields(doc):
    for field in FIELD_MAP:
        doc.set(field, '')

def update_taxpayer_fields(doc, taxpayer_info, code_mappings):
    for field, api_key in FIELD_MAP.items():
        value = taxpayer_info.get(api_key, '')
        
        # Apply code mappings if available
        if api_key in code_mappings:
            mapped_value = code_mappings[api_key].get(value, value)
            value = f"{value} | {mapped_value}"
        
        doc.set(field, value)

def fetch_taxpayer_info(tin):
    return frappe.call(
        "ura_efris.api.t119_query_taxpayer_info.t119_send_request",
        tin=tin
    )


def update_taxpayer_information(doc, method):
    previous_doc = doc.get_doc_before_save()

    # Check for new documents or changed tax_id
    if not previous_doc or previous_doc.tax_id != doc.tax_id:
        if not doc.tax_id:
            clear_taxpayer_fields(doc)
        else:
            return_code, return_message, return_content = fetch_taxpayer_info(doc.tax_id)
            if return_code == "00" and return_message == "SUCCESS":
                taxpayer_info = return_content.get('taxpayer', {})
                code_mappings = get_code_mapping("T119")
                update_taxpayer_fields(doc, taxpayer_info, code_mappings)
                frappe.msgprint(
                    msg=f"TIN: {doc.tax_id}<br> Legal Name: {doc.legal_name}",
                    title="Taxpayer Information",
                    indicator="green"
                )
            else:
                frappe.msgprint(
                    msg = f"{return_code}: {return_message}<br>Please verify the Tax ID.",
                    title="Taxpayer Information",
                    indicator="red"
                )

