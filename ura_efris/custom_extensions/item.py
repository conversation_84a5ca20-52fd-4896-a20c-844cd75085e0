import frappe
from frappe.utils import today

def process_efris_item(doc, method):
    if doc.is_efris != 1:
        return

    update_item_price(doc)
    upload_item_to_efris(doc)

def update_item_price(doc):
    price_list = frappe.db.get_single_value("Selling Settings", "selling_price_list")
    if not price_list:
        frappe.throw(
            msg="Selling Price List is not configured in Selling Settings",
            title="Configuration Error"
        )

    query = """
        SELECT price_list_rate
        FROM `tabItem Price`
        WHERE
            item_code = %(item_code)s
            AND uom = %(uom)s
            AND valid_from <= %(current_date)s
            AND (valid_upto IS NULL OR valid_upto >= %(current_date)s)
            AND price_list = %(price_list)s
        ORDER BY valid_from DESC
        LIMIT 1
    """
    item_price = frappe.db.sql(
        query,
        {
            "item_code": doc.item_code,
            "uom": doc.stock_uom,
            "current_date": today(),
            "price_list": price_list,
        },
        as_dict=True,
    )

    if item_price:
        doc.unit_price = item_price[0]["price_list_rate"]
    else:
        frappe.throw(
            msg=f"No valid price found for item {doc.item_code} with UOM {doc.stock_uom}",
            title="Price Not Found"
        )

def check_existing_efris_item(item_code):
    return_code, return_message, return_content = frappe.call(
        "ura_efris.api.t144_goods_services_inquiry.t144_send_request",
        item_code=item_code,
    )
    if return_code == "00" and return_message == "SUCCESS" and return_content:
        return return_content[0].get("id")
    return None

def upload_item_to_efris(doc):
    efris_id = check_existing_efris_item(doc.item_code)
    if efris_id:
        doc.efris_id = efris_id
    operation_type = "102" if doc.efris_id else "101"  # 101: Add, 102: Modify
    return_code, return_message, return_content = frappe.call(
        "ura_efris.api.t130_goods_upload.t130_send_request",
        doc=doc,
        operation_type = operation_type       #101: add goods(default), 102: modify product
    )

    if return_code == "00" and return_message == "SUCCESS":
        doc.return_code = return_code
        doc.return_message = return_message
        frappe.msgprint(
            msg=f"Goods uploaded successfully.",
            title="EFRIS",
            indicator="green"
        )
    else:
        doc.return_code = return_content[0].get("returnCode")
        doc.return_message = return_content[0].get("returnMessage")
        frappe.msgprint(
            msg=f"Error: <b>{doc.return_code}</b> - {doc.return_message}",
            title="EFRIS",
            indicator="red"
        )
    